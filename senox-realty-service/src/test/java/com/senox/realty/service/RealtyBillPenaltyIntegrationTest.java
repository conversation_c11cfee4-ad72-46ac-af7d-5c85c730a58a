package com.senox.realty.service;

import com.senox.common.utils.DateUtils;
import com.senox.context.AdminUserDto;
import com.senox.pm.constant.TaxCategory;
import com.senox.realty.BaseTest;
import com.senox.realty.constant.*;
import com.senox.realty.domain.*;
import com.senox.realty.vo.BillMonthVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 滞纳金功能集成测试 - 使用真实数据
 * 测试租赁/物业合同停用后不计算滞纳金的功能
 */
@Slf4j
class RealtyBillPenaltyIntegrationTest extends BaseTest {

    @Autowired
    private ContractService contractService;

    @Autowired
    private RealtyBillService realtyBillService;

    @Autowired
    private RealtyBillPenaltyService realtyBillPenaltyService;

    @Autowired
    private ContractBillService contractBillService;

    @Autowired
    private RealtyService realtyService;

    @Autowired
    private FeeService feeService;

    /**
     * 测试租赁合同停用后不计算滞纳金 - 使用真实数据
     */
    @Test
    @Transactional
    void testSuspendedLeaseContractNoPenaltyWithRealData() {
        log.info("=== 开始测试：租赁合同停用后不计算滞纳金（真实数据） ===");

        // 1. 创建物业
        Realty realty = createTestRealty();
        Long realtyId = realtyService.addRealty(realty, null);
        log.info("创建测试物业，ID: {}, 编号: {}", realtyId, realty.getSerialNo());

        // 2. 创建租赁合同
        Contract contract = createTestContract(ContractType.LEASE, realtyId);
        ContractExt contractExt = createTestContractExt();
        List<ContractFee> contractFees = createTestContractFees();

        Long contractId = contractService.addContract(contract, contractExt, contractFees);
        log.info("创建测试租赁合同，ID: {}, 合同号: {}", contractId, contract.getContractNo());

        // 3. 生成账单
        AdminUserDto operator = createTestOperator();
        BillMonthVo billMonth = new BillMonthVo();
        billMonth.setYear(LocalDate.now().getYear());
        billMonth.setMonth(LocalDate.now().getMonthValue());
        billMonth.setContractNo(contract.getContractNo());

        contractBillService.buildAndSaveBill(billMonth, operator);
        log.info("生成测试账单，年月: {}-{}", billMonth.getYear(), billMonth.getMonth());

        // 4. 验证账单已生成
        RealtyBill bill = realtyBillService.findMonthlyBillByContractNo(contract.getContractNo(), billMonth.getYear(), billMonth.getMonth());
        Assertions.assertNotNull(bill, "账单应该已生成");
        log.info("验证账单已生成，账单ID: {}", bill.getId());

        // 5. 计算滞纳金（合同启用状态）
        realtyBillPenaltyService.calBillPenalty(contract.getContractNo());
        log.info("启用状态下计算滞纳金完成");

        // 6. 停用合同
        Contract updateContract = new Contract();
        updateContract.setId(contractId);
        updateContract.setStatus(ContractStatus.SUSPEND.ordinal());
        updateContract.setStopBy(operator.getUserId());
        updateContract.setStopTime(LocalDateTime.now());
        updateContract.setModifierId(operator.getUserId());
        updateContract.setModifierName(operator.getUsername());

        contractService.updateContract(updateContract, null, null);
        log.info("合同已停用，合同号: {}", contract.getContractNo());

        // 7. 再次计算滞纳金（合同停用状态）
        realtyBillPenaltyService.calBillPenalty(contract.getContractNo());
        log.info("停用状态下计算滞纳金完成 - 应该跳过计算");

        log.info("=== 测试完成：租赁合同停用后不计算滞纳金（真实数据） ===");
    }

    /**
     * 测试物业合同停用后不计算滞纳金 - 使用真实数据
     */
    @Test
    @Transactional
    void testSuspendedEstateContractNoPenaltyWithRealData() {
        log.info("=== 开始测试：物业合同停用后不计算滞纳金（真实数据） ===");

        // 1. 创建物业
        Realty realty = createTestRealty();
        Long realtyId = realtyService.addRealty(realty, null);
        log.info("创建测试物业，ID: {}, 编号: {}", realtyId, realty.getSerialNo());

        // 2. 创建物业合同
        Contract contract = createTestContract(ContractType.ESTATE, realtyId);
        ContractExt contractExt = createTestContractExt();
        List<ContractFee> contractFees = createTestContractFees();

        Long contractId = contractService.addContract(contract, contractExt, contractFees);
        log.info("创建测试物业合同，ID: {}, 合同号: {}", contractId, contract.getContractNo());

        // 3. 生成账单
        AdminUserDto operator = createTestOperator();
        BillMonthVo billMonth = new BillMonthVo();
        billMonth.setYear(LocalDate.now().getYear());
        billMonth.setMonth(LocalDate.now().getMonthValue());
        billMonth.setContractNo(contract.getContractNo());

        contractBillService.buildAndSaveBill(billMonth, operator);
        log.info("生成测试账单，年月: {}-{}", billMonth.getYear(), billMonth.getMonth());

        // 4. 验证账单已生成
        RealtyBill bill = realtyBillService.findMonthlyBillByContractNo(contract.getContractNo(), billMonth.getYear(), billMonth.getMonth());
        Assertions.assertNotNull(bill, "账单应该已生成");
        log.info("验证账单已生成，账单ID: {}", bill.getId());

        // 5. 计算滞纳金（合同启用状态）
        realtyBillPenaltyService.calBillPenalty(contract.getContractNo());
        log.info("启用状态下计算滞纳金完成");

        // 6. 停用合同
        Contract updateContract = new Contract();
        updateContract.setId(contractId);
        updateContract.setStatus(ContractStatus.SUSPEND.ordinal());
        updateContract.setStopBy(operator.getUserId());
        updateContract.setStopTime(LocalDateTime.now());
        updateContract.setModifierId(operator.getUserId());
        updateContract.setModifierName(operator.getUsername());

        contractService.updateContract(updateContract, null, null);
        log.info("合同已停用，合同号: {}", contract.getContractNo());

        // 7. 再次计算滞纳金（合同停用状态）
        realtyBillPenaltyService.calBillPenalty(contract.getContractNo());
        log.info("停用状态下计算滞纳金完成 - 应该跳过计算");

        log.info("=== 测试完成：物业合同停用后不计算滞纳金（真实数据） ===");
    }

    /**
     * 测试其他类型合同不受停用规则影响 - 使用真实数据
     */
    @Test
    @Transactional
    void testOtherContractTypesNotAffectedWithRealData() {
        log.info("=== 开始测试：其他类型合同不受停用规则影响（真实数据） ===");

        // 测试返租合同
        testContractTypeNotAffected(ContractType.LEASEBACK, "返租");

        // 测试代租合同
        testContractTypeNotAffected(ContractType.RENT_PROXY, "代租");

        // 测试代收租合同
        testContractTypeNotAffected(ContractType.RENT_COLLECTION_PROXY, "代收租");

        log.info("=== 测试完成：其他类型合同不受停用规则影响（真实数据） ===");
    }

    /**
     * 测试指定类型合同不受停用规则影响
     */
    private void testContractTypeNotAffected(ContractType contractType, String typeName) {
        log.info("--- 测试{}合同不受停用规则影响 ---", typeName);

        // 1. 创建物业
        Realty realty = createTestRealty();
        Long realtyId = realtyService.addRealty(realty, null);

        // 2. 创建合同
        Contract contract = createTestContract(contractType, realtyId);
        ContractExt contractExt = createTestContractExt();
        List<ContractFee> contractFees = createTestContractFees();

        Long contractId = contractService.addContract(contract, contractExt, contractFees);
        log.info("创建测试{}合同，合同号: {}", typeName, contract.getContractNo());

        // 3. 生成账单
        AdminUserDto operator = createTestOperator();
        BillMonthVo billMonth = new BillMonthVo();
        billMonth.setYear(LocalDate.now().getYear());
        billMonth.setMonth(LocalDate.now().getMonthValue());
        billMonth.setContractNo(contract.getContractNo());

        contractBillService.buildAndSaveBill(billMonth, operator);

        // 4. 停用合同
        Contract updateContract = new Contract();
        updateContract.setId(contractId);
        updateContract.setStatus(ContractStatus.SUSPEND.ordinal());
        updateContract.setStopBy(operator.getUserId());
        updateContract.setStopTime(LocalDateTime.now());
        updateContract.setModifierId(operator.getUserId());
        updateContract.setModifierName(operator.getUsername());

        contractService.updateContract(updateContract, null, null);
        log.info("{}合同已停用", typeName);

        // 5. 计算滞纳金（应该正常计算，不受停用规则影响）
        realtyBillPenaltyService.calBillPenalty(contract.getContractNo());
        log.info("{}合同停用状态下计算滞纳金完成 - 应该正常计算", typeName);
    }

    /**
     * 创建测试物业
     */
    private Realty createTestRealty() {
        Realty realty = new Realty();
        realty.setSerialNo("TEST-" + randStr(10));
        realty.setName("测试物业-" + randStr(5));
        realty.setNature(RealtyNature.COMPANY.ordinal());
        realty.setRegion1("测试区域1");
        realty.setRegion2("测试区域2");
        realty.setAddress("测试地址");
        realty.setArea(randDecimal(BigDecimal.valueOf(50), BigDecimal.valueOf(200), 2));
        realty.setCreatorId(1L);
        realty.setCreatorName("测试用户");
        realty.setModifierId(1L);
        realty.setModifierName("测试用户");
        return realty;
    }

    /**
     * 创建测试合同
     */
    private Contract createTestContract(ContractType contractType, Long realtyId) {
        Contract contract = new Contract();
        // 确保合同号长度不超过30个字符
        String contractPrefix = contractType.name().substring(0, Math.min(contractType.name().length(), 15));
        contract.setContractNo(contractPrefix + "-" + randStr(8));
        contract.setOrderNo(randStr(12));
        contract.setType(contractType.getValue());
        contract.setRealtyId(realtyId);
        contract.setRealtyName("测试物业");
        contract.setCustomerId(randLong(1L, 100L));
        contract.setCustomerName("测试客户-" + randStr(5));
        contract.setStatus(ContractStatus.EFFECTIVE.ordinal());
        contract.setSignDate(LocalDate.now().minusDays(30));
        contract.setStartDate(LocalDate.now().minusDays(30));
        contract.setEndDate(LocalDate.now().plusYears(1));
        contract.setCreatorId(1L);
        contract.setCreatorName("测试用户");
        contract.setModifierId(1L);
        contract.setModifierName("测试用户");
        contract.setCategory(TaxCategory.PERSONAL.getValue());
        return contract;
    }

    /**
     * 创建测试合同扩展信息
     */
    private ContractExt createTestContractExt() {
        ContractExt ext = new ContractExt();
        ext.setCostType(CostType.CASH.getValue());
        ext.setFirstRate(BigDecimal.valueOf(0.05));
        ext.setMonthlyRate(BigDecimal.valueOf(0.05));
        ext.setMonthlyFeeAbs(BigDecimal.valueOf(50));
        ext.setPenaltyStartDate(15); // 每月15号开始计算滞纳金
        ext.setRemark("测试合同扩展信息");
        return ext;
    }
g
    /**
     * 创建测试合同费项
     */
    private List<ContractFee> createTestContractFees() {
        // 获取系统中的费项
        List<Fee> fees = feeService.listAll();
        log.info("系统中的费项数量: {}", fees.size());

        if (fees.isEmpty()) {
            log.warn("系统中没有费项，无法创建合同费项");
            return Arrays.asList();
        }

        ContractFee rentFee = new ContractFee();
        rentFee.setFeeId(fees.get(0).getId()); // 使用第一个费项作为租金
        rentFee.setCategory(ContractFeeCategory.DEFAULT.ordinal());
        rentFee.setAmount(BigDecimal.valueOf(5000));
        rentFee.setStartDate(LocalDate.now().minusDays(30));
        rentFee.setEndDate(LocalDate.now().plusYears(1));
        log.info("创建租金费项，费项ID: {}, 金额: {}", rentFee.getFeeId(), rentFee.getAmount());

        List<ContractFee> contractFees = new ArrayList<>();
        contractFees.add(rentFee);

        if (fees.size() > 1) {
            ContractFee manageFee = new ContractFee();
            manageFee.setFeeId(fees.get(1).getId()); // 使用第二个费项作为管理费
            manageFee.setCategory(ContractFeeCategory.DEFAULT.ordinal());
            manageFee.setAmount(BigDecimal.valueOf(500));
            manageFee.setStartDate(LocalDate.now().minusDays(30));
            manageFee.setEndDate(LocalDate.now().plusYears(1));
            contractFees.add(manageFee);
            log.info("创建管理费项，费项ID: {}, 金额: {}", manageFee.getFeeId(), manageFee.getAmount());
        }

        return contractFees;
    }

    /**
     * 创建测试操作员
     */
    private AdminUserDto createTestOperator() {
        AdminUserDto operator = new AdminUserDto();
        operator.setUserId(1L);
        operator.setUsername("测试操作员");
        operator.setToken("test-token");
        return operator;
    }
}