package com.senox.realty.service;

import com.senox.common.vo.PageResult;
import com.senox.pm.api.clients.ReceiptApplyClient;
import com.senox.pm.api.clients.ReceiptClient;
import com.senox.pm.constant.ReceiptStatus;
import com.senox.pm.constant.ReceiptType;
import com.senox.pm.constant.TaxCategory;
import com.senox.pm.vo.ReceiptApplySearchVo;
import com.senox.pm.vo.ReceiptApplyVo;
import com.senox.pm.vo.TaxHeaderVo;
import com.senox.realty.BaseTest;
import com.senox.realty.config.ReceiptConfig;
import com.senox.realty.vo.RealtyReceiptMangerVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-7-20
 */
@Slf4j
class RealtyBillServiceTest extends BaseTest {

    @Autowired
    private RealtyReceiptService  realtyReceiptService;
    @Autowired
    private ReceiptConfig receiptConfig;
    @MockBean
    private ReceiptClient receiptClient;
    @Autowired
    private ReceiptApplyClient receiptApplyClient;

    // @BeforeEach
    void receiptConfigTest() {
        Assertions.assertFalse(CollectionUtils.isEmpty(receiptConfig.getGoods()));
        Mockito.doReturn(mockTaxHeader()).when(receiptClient).getTaxHeader(99L);
    }

    // @Test
    void auditTest(){
        realtyReceiptService.audit(1L, ReceiptStatus.AUDIT_APPROVED,"",1L);
    }

    //@Test
    void receiptApplyListTest(){
        ReceiptApplySearchVo search = new ReceiptApplySearchVo();
        search.setPageNo(1);
        search.setPageSize(10);
        search.setPage(false);
        PageResult<ReceiptApplyVo> pageResult = receiptApplyClient.list(search);
        List<ReceiptApplyVo> receiptApplyList = pageResult.getDataList();



    }

    @Autowired
    private RealtyBillPenaltyService realtyBillPenaltyService;
    @Autowired
    private ContractService contractService;
    @Autowired
    private RealtyBillService realtyBillService;

    /**
     * 测试租赁合同停用后不计算滞纳金
     */
    @Test
    void testSuspendedLeaseContractNoPenalty() {
        // 模拟一个已停用的租赁合同
        String contractNo = "LEASE-SUSPENDED-001";

        // 调用滞纳金计算 - 应该跳过已停用的租赁合同
        realtyBillPenaltyService.calBillPenalty(contractNo);

        // 验证：通过日志可以看到"租赁合同 xxx 已停用，账单 xxx 不计算滞纳金"
        log.info("测试完成：已停用的租赁合同应该不计算滞纳金");
    }

    /**
     * 测试物业合同停用后不计算滞纳金
     */
    @Test
    void testSuspendedEstateContractNoPenalty() {
        // 模拟一个已停用的物业合同
        String contractNo = "ESTATE-SUSPENDED-001";

        // 调用滞纳金计算 - 应该跳过已停用的物业合同
        realtyBillPenaltyService.calBillPenalty(contractNo);

        // 验证：通过日志可以看到"物业合同 xxx 已停用，账单 xxx 不计算滞纳金"
        log.info("测试完成：已停用的物业合同应该不计算滞纳金");
    }

    /**
     * 测试其他类型合同（返租、代租、代收租）正常计算滞纳金
     */
    @Test
    void testOtherContractTypesNormalPenalty() {
        // 模拟返租合同
        String leasebackContractNo = "LEASEBACK-ACTIVE-001";
        realtyBillPenaltyService.calBillPenalty(leasebackContractNo);

        // 模拟代租合同
        String rentProxyContractNo = "RENTPROXY-ACTIVE-001";
        realtyBillPenaltyService.calBillPenalty(rentProxyContractNo);

        // 模拟代收租合同
        String rentCollectionContractNo = "RENTCOLLECTION-ACTIVE-001";
        realtyBillPenaltyService.calBillPenalty(rentCollectionContractNo);

        // 验证：其他类型合同不受停用规则影响，应该正常计算滞纳金
        log.info("测试完成：返租、代租、代收租合同应该正常计算滞纳金");
    }

    /**
     * 测试启用状态的租赁/物业合同正常计算滞纳金
     */
    @Test
    void testActiveLeaseEstateContractNormalPenalty() {
        // 模拟启用状态的租赁合同
        String activeLeaseContractNo = "LEASE-ACTIVE-001";
        realtyBillPenaltyService.calBillPenalty(activeLeaseContractNo);

        // 模拟启用状态的物业合同
        String activeEstateContractNo = "ESTATE-ACTIVE-001";
        realtyBillPenaltyService.calBillPenalty(activeEstateContractNo);

        // 验证：启用状态的租赁/物业合同应该正常计算滞纳金
        log.info("测试完成：启用状态的租赁/物业合同应该正常计算滞纳金");
    }

    private TaxHeaderVo mockTaxHeader(){
        TaxHeaderVo taxHeaderVo = new TaxHeaderVo();
        taxHeaderVo.setId(99L);
        taxHeaderVo.setHeader("东莞市金永利粮油食品有限公司");
        taxHeaderVo.setSerial("9144190073461310P");
        taxHeaderVo.setRegisterAddress("address");
        taxHeaderVo.setRegisterMobile("***********");
        taxHeaderVo.setEnterpriseBankAccount("11");
        taxHeaderVo.setOpenid("oGxwBv1L5yEEeyaJb5wyZ9TDsQho");
        taxHeaderVo.setCategory(TaxCategory.COMPANY.getValue());
        return taxHeaderVo;
    }
}
